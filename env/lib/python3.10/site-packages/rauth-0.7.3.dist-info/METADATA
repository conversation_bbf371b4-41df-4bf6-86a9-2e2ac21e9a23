Metadata-Version: 2.1
Name: rauth
Version: 0.7.3
Summary: A Python library for OAuth 1.0/a, 2.0, and Ofly.
Home-page: https://github.com/litl/rauth
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Keywords: oauth oauth2 rauth requests
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Operating System :: MacOS
Classifier: Operating System :: POSIX
Classifier: Operating System :: POSIX :: Linux
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: Implementation
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Internet :: WWW/HTTP
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Utilities
License-File: LICENSE
Requires-Dist: requests >=1.2.3


    rauth
    -----

    A simple Python OAuth 1.0/a, OAuth 2.0, and Ofly consumer library built on
    top of Requests.

    Links
    `````
    * `documentation <https://rauth.readthedocs.org/en/latest/>`_
    * `development version <https://github.com/maxcountryman/rauth>`_


