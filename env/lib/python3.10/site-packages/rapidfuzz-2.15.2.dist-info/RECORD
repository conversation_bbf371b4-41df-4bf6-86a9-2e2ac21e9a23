rapidfuzz-2.15.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
rapidfuzz-2.15.2.dist-info/LICENSE,sha256=80zN_9_1b8QXeFkV7t8YYB1UeXVd88RyssdUwpgC1oM,1092
rapidfuzz-2.15.2.dist-info/METADATA,sha256=GXPcSe2DbaZJ9Vj-x26-e4VbUXP9rchrXqH_l22VFIA,9022
rapidfuzz-2.15.2.dist-info/RECORD,,
rapidfuzz-2.15.2.dist-info/WHEEL,sha256=cSmQbkNDab36KQmXphd83nSZDzoFkLydYC11op1qL-Y,146
rapidfuzz-2.15.2.dist-info/entry_points.txt,sha256=kOVpfhav7S0YoUt7-V7YavLAJ_G4oXeva2JCc9JvXcA,120
rapidfuzz-2.15.2.dist-info/top_level.txt,sha256=Oxm3Kk-v28hdkOkW3AVE1EMl8Ml5FPFl32w6V65shvg,10
rapidfuzz/__init__.pxd,sha256=oId8bYEldzcC2S9aue8bkV696JJkYlZDUEdE0bCpEBc,2182
rapidfuzz/__init__.py,sha256=pLE8pkGdUl5lRXnOplzVSfd4Otw3kGXJN8yJAV5MT2g,815
rapidfuzz/__init__.pyi,sha256=c-CbDSRPdSlsWhPV45yMwuZfcRu_q6YrM1KfKyZXeJw,260
rapidfuzz/__pycache__/__init__.cpython-310.pyc,,
rapidfuzz/__pycache__/_feature_detector.cpython-310.pyc,,
rapidfuzz/__pycache__/_utils.cpython-310.pyc,,
rapidfuzz/__pycache__/fuzz.cpython-310.pyc,,
rapidfuzz/__pycache__/fuzz_cpp.cpython-310.pyc,,
rapidfuzz/__pycache__/fuzz_py.cpython-310.pyc,,
rapidfuzz/__pycache__/process.cpython-310.pyc,,
rapidfuzz/__pycache__/process_cpp.cpython-310.pyc,,
rapidfuzz/__pycache__/process_py.cpython-310.pyc,,
rapidfuzz/__pycache__/string_metric.cpython-310.pyc,,
rapidfuzz/__pycache__/utils.cpython-310.pyc,,
rapidfuzz/__pycache__/utils_py.cpython-310.pyc,,
rapidfuzz/__pyinstaller/__init__.py,sha256=uLxcndXJQC67tSw5v8VDT9Qv67w0VbVXHYPwpOloZEc,142
rapidfuzz/__pyinstaller/__pycache__/__init__.cpython-310.pyc,,
rapidfuzz/__pyinstaller/__pycache__/hook-rapidfuzz.cpython-310.pyc,,
rapidfuzz/__pyinstaller/__pycache__/test_rapidfuzz_packaging.cpython-310.pyc,,
rapidfuzz/__pyinstaller/hook-rapidfuzz.py,sha256=wyHoFtra4LacFcx7SjV7DVmH7cqOu0E29mwtpKG7_wA,1158
rapidfuzz/__pyinstaller/test_rapidfuzz_packaging.py,sha256=dpaKmtPd85RhP5hLPxbPhUwBMBWgeM3Ts3CIJoQkbj8,1070
rapidfuzz/_feature_detector.py,sha256=Xn6IQjzwG9EUCdWv4i0Z9NQt6hr9DTofesjdaAyjqRA,311
rapidfuzz/_feature_detector_cpp.cpython-310-x86_64-linux-gnu.so,sha256=uOQjUAtLVuUo5NiwViosUYfGOQgTE_QgWfvu4vRTm68,56424
rapidfuzz/_utils.py,sha256=fcJh1u-53EiSTUrg-jT-Yv748DAYYxvUAaA-RFZGf2s,3572
rapidfuzz/distance/DamerauLevenshtein.py,sha256=FRLnMGPoYY9w7HJRNY6g67GqLk626eZFHTzlI9UxMhw,909
rapidfuzz/distance/DamerauLevenshtein.pyi,sha256=W33zaUdPGNAuY3qIdLM6cYhxTK187cnZSUjWAhfFPhA,953
rapidfuzz/distance/DamerauLevenshtein_cpp.py,sha256=RViNJBakr8_qt93_V4b0n0dzf2cVQpD6Rhh3Bluhazw,1180
rapidfuzz/distance/DamerauLevenshtein_py.py,sha256=GtlSDOpWwms8_OQg23C5c8WNDSG54M5tmRsIwY_PTfI,6927
rapidfuzz/distance/Hamming.py,sha256=mOtSJyPiX7GPw8o3xOrn59QOPVJs1NFE0p_Pfl6eIRA,986
rapidfuzz/distance/Hamming.pyi,sha256=UWYsDcGeT4V6BIOD87fm4n3OpFznX9qVp3izKBKibSk,1312
rapidfuzz/distance/Hamming_cpp.py,sha256=XNUwj7O7UwtIr14gzrQYZjncJ8omkEqH6rERVhtVuI8,1388
rapidfuzz/distance/Hamming_py.py,sha256=qk236sQr2pIGR_9n5IiHGJyVX9XhrNW58pjMODEeyww,7754
rapidfuzz/distance/Indel.py,sha256=9yN7cxekuoj4qHQDzIQQihhD77DS4SrwINn8zFB2r28,984
rapidfuzz/distance/Indel.pyi,sha256=UWYsDcGeT4V6BIOD87fm4n3OpFznX9qVp3izKBKibSk,1312
rapidfuzz/distance/Indel_cpp.py,sha256=45ZEe1TkbP3kpVY2D36sUXIf2nMS766eRRmOuwgEZHQ,1364
rapidfuzz/distance/Indel_py.py,sha256=Apg01X4t7tZVzXZCLCjtpKcVN0rsal3IFySHFqWVmgE,10703
rapidfuzz/distance/Jaro.py,sha256=2Ch6DDxQLVI8UoZFzGJ0kZNsDnfhKEWGZQ3OXB4VYIg,764
rapidfuzz/distance/Jaro.pyi,sha256=0imhX6IEzlyehNt330u-WYQHuHiSdL4wttq3QxNQr84,955
rapidfuzz/distance/JaroWinkler.py,sha256=1mdKdOg42IbZT_OBReL9_dGJKEPlCOMkz_TY9rMLr7M,771
rapidfuzz/distance/JaroWinkler.pyi,sha256=yXxMDNCUnr_EmR-vHeQ5BMDIiRwycgODuSwe3j1w5CY,1083
rapidfuzz/distance/JaroWinkler_cpp.py,sha256=_kNAKSzU-nb3h0xEQ9HBbJYyGkFPjhpaPCO_Vce3l34,1090
rapidfuzz/distance/JaroWinkler_py.py,sha256=0sXTcmv6shkjA-5be1ZJ4-a7S-o6dYiE101oym02mZ0,6732
rapidfuzz/distance/Jaro_cpp.py,sha256=mWFZw-Z8E8vd8T1NmFWRKvXwk3q_QooJzJk1O1ZxIPA,1009
rapidfuzz/distance/Jaro_py.py,sha256=nTDhL1bJz17VU6PUG8CnQwyIEtRIF6c9xKtTrE81bKc,7878
rapidfuzz/distance/LCSseq.py,sha256=pSKeATqBLDWQ62vgSKlZHiTqI5ivab0zTuqZTjKj8fU,985
rapidfuzz/distance/LCSseq.pyi,sha256=UWYsDcGeT4V6BIOD87fm4n3OpFznX9qVp3izKBKibSk,1312
rapidfuzz/distance/LCSseq_cpp.py,sha256=AEwRKQ3w9Uodqvl1C8CDET80A1ymtp6Jr0JwPvQT4UM,1388
rapidfuzz/distance/LCSseq_py.py,sha256=hmwl36poyO05voMC9sFoUIjJHUcNIbH5JNVvjvgIXlQ,10105
rapidfuzz/distance/Levenshtein.py,sha256=-uxmz6cg3CfKJG9wEPObDbMRze9ycVo51hXXQhEg4Rs,2786
rapidfuzz/distance/Levenshtein.pyi,sha256=cBoefrQQ9cOw8TXnd4_PtYbkuJ227BRPuHmNaB7Ox0w,1973
rapidfuzz/distance/Levenshtein_cpp.py,sha256=HrrPoca3BI7Hn6jTUCabrgN1lqTWe47RM5qrAu3zZoI,1436
rapidfuzz/distance/Levenshtein_py.py,sha256=1oOnd3LkppYSzXVbnPDyHEgr3u3r1RdMW44FBvlcuMY,15424
rapidfuzz/distance/OSA.py,sha256=3yStHciGq6_WxaavkZy9WjnBY6aHvyobgJEfy_18ImA,894
rapidfuzz/distance/OSA.pyi,sha256=3OGnmJCdX5GppJzH-zzoqNQgLQdLG89Ur712ikFLe4U,947
rapidfuzz/distance/OSA_cpp.py,sha256=kTOV_565ny-PgghMd8O7aJ7ER4HAKzWM2Gd3DomH2Rg,1001
rapidfuzz/distance/OSA_py.py,sha256=GWTfvh3a7bgqB62fvzEvoMDwWlTgZw94714wlzTW_bo,6556
rapidfuzz/distance/Postfix.py,sha256=CvEszjrm3rSntOqp9QoATrURYtY8W_IMFqF8-BmOBOY,897
rapidfuzz/distance/Postfix_cpp.py,sha256=fuuEGdcGGXzrCHquwBloSRc03_09GGr-TxlU8X7ZyVU,1052
rapidfuzz/distance/Postfix_py.py,sha256=fw7GbhNIj3E9SSwnE94Z1IVvOuAPossUMuYEYVoCnIc,5312
rapidfuzz/distance/Prefix.py,sha256=7uFUzFP1e-YoYQcha6SmcQ7zwJsAWV5xV62OQQcT-EE,896
rapidfuzz/distance/Prefix_cpp.py,sha256=_WtrEnY-FZ0dcKO3X43vXQKPJzrEFCv6nFOA1dgB3ys,1044
rapidfuzz/distance/Prefix_py.py,sha256=GBr3HRSQrjnbkklpIK1bFj0PMPARqJSPQA88yenrlzQ,5288
rapidfuzz/distance/__init__.py,sha256=4DnHc3o3Bs6xUyFJTQKqPgenKSuk3Wx80IcUuzRgp0U,621
rapidfuzz/distance/__init__.pyi,sha256=AbXnL-YRqTSFeY0vi5m0Z4kA-izYJA6JMXJffunq2qE,734
rapidfuzz/distance/__pycache__/DamerauLevenshtein.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/DamerauLevenshtein_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/DamerauLevenshtein_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Hamming.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Hamming_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Hamming_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Indel.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Indel_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Indel_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Jaro.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/JaroWinkler.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/JaroWinkler_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/JaroWinkler_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Jaro_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Jaro_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/LCSseq.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/LCSseq_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/LCSseq_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Levenshtein.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Levenshtein_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Levenshtein_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/OSA.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/OSA_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/OSA_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Postfix.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Postfix_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Postfix_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Prefix.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Prefix_cpp.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Prefix_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/__init__.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/_initialize.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/_initialize_py.cpython-310.pyc,,
rapidfuzz/distance/_initialize.py,sha256=bV-wP__DKRUAqv9SNLIc-eT95J5RCB6OLwNgQvs124Y,646
rapidfuzz/distance/_initialize.pyi,sha256=_vnDEbCt4k60J2qvdkJPJXpiqDaijzsY1MK-bH_riGI,4052
rapidfuzz/distance/_initialize_cpp.cpython-310-x86_64-linux-gnu.so,sha256=EEWc-GqZaTqSxJp0CX2jLPJQGGEBGBoXfu4SaaED_1E,540768
rapidfuzz/distance/_initialize_py.py,sha256=71tzELzNODXMfWWrYZoiKbtp1YwNE1kcqIcsjg8ui-Y,28535
rapidfuzz/distance/metrics_cpp.cpython-310-x86_64-linux-gnu.so,sha256=dM6dgCsGQY0jkS_PR3gWV8VTEl8Wob9YegTy7U2qRNs,2679048
rapidfuzz/distance/metrics_cpp.pyi,sha256=m9zwOxwpUgWNwCVC5JPdB08hevF_wYykm2tNyy621Mg,7338
rapidfuzz/distance/metrics_cpp_avx2.cpython-310-x86_64-linux-gnu.so,sha256=qbkb0AVms1ePrqgNL4ygALVmk8W1bt_3vIw30rSMgD0,2821880
rapidfuzz/fuzz.py,sha256=mHE6RBNshPfiV78AjL205ldcSPBKLqR_tHJKuiAA-3o,1952
rapidfuzz/fuzz.pyi,sha256=BhRT4w3SJRQfY61IJFRpu4-pqVlbGsKkG-3dfdB9ZYA,2542
rapidfuzz/fuzz_cpp.py,sha256=bijOpit5niKjtYEjTTzZMW2IFBYpQo3adEJYPkkUf6M,1089
rapidfuzz/fuzz_cpp_impl.cpython-310-x86_64-linux-gnu.so,sha256=LghxY-aOqER8PtoFlQpHkQQWq_PatiNzZBlxIydw1S0,2054784
rapidfuzz/fuzz_cpp_impl_avx2.cpython-310-x86_64-linux-gnu.so,sha256=V_eRvl6TGRl3VWVRVyevqWgPepcIJ5WlA8zDLvMPaW4,2073440
rapidfuzz/fuzz_py.py,sha256=leF-_PNuKXbjTxKMb36OPewKdaGHnIgS-_Qt20z-fuc,25774
rapidfuzz/process.py,sha256=RTtVoc-hGlvL6kFa63rCkNe37I4urLAB7XM4KGi3bt4,397
rapidfuzz/process.pyi,sha256=v1OmQDopqPKfJPkG1YQwX0QklNvc1-mpHF1US_bZiek,2886
rapidfuzz/process_cpp.py,sha256=dgf2MfvKA3NgK2MJ9cZEY-vpuisdv0jBWOGto1vQVSI,2460
rapidfuzz/process_cpp_impl.cpython-310-x86_64-linux-gnu.so,sha256=svRhLFGVYNrpKtsgWPgZjCUw5hvSBlpTeFc6XTjBhgo,830312
rapidfuzz/process_cpp_impl.pyi,sha256=YQ_hx4pIevq2mpVp11dnkGeOet2BDiOmxzMfjTqSDQE,2702
rapidfuzz/process_py.py,sha256=MdfQv8KjcH284TPeAYjsiQ8LSPiRUYiC08vXGTFyTPA,25998
rapidfuzz/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
rapidfuzz/rapidfuzz.h,sha256=opBNZ96-ajRHq4J7T8lQnX0qOLaXWAkr8ve-Bhnj-d4,6587
rapidfuzz/string_metric.py,sha256=poYJPVBORZW410QORkWhXfIZP0pUfpxd_UwB9e5QbYU,13513
rapidfuzz/utils.py,sha256=GrBknXCRc_kMAFfJnP7aAdobqaK7oIAxr3sHtpazAuY,272
rapidfuzz/utils.pyi,sha256=DuvrI_7mTQWRA88mUwmLTXgLK2TlKRhJyZNthNAFrOM,218
rapidfuzz/utils_cpp.cpython-310-x86_64-linux-gnu.so,sha256=0CJLJHvEi-EH-FptOu7j5LUdVZPK243c9sSLsyEtBkU,362952
rapidfuzz/utils_py.py,sha256=oP6xgOsopkR0NhNjf3u6x55gw03LUMJ1zh2FYIsxno4,622
